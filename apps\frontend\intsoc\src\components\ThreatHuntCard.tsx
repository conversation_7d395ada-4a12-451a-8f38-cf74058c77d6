'use client';

import { useState } from 'react';
import { Card, CardHeader, CardContent } from '@telesoft/ui';
import { ThreatHunt } from '@telesoft/types';

interface ThreatHuntCardProps {
  hunt: ThreatHunt;
  onExpand?: (hunt: ThreatHunt) => void;
}

export function ThreatHuntCard({ hunt, onExpand }: ThreatHuntCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleExpand = () => {
    setIsExpanded(!isExpanded);
    if (onExpand) {
      onExpand(hunt);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'text-cyber-matrix-400 bg-cyber-matrix-400/10';
      case 'completed':
        return 'text-green-400 bg-green-400/10';
      case 'failed':
        return 'text-cyber-critical-400 bg-cyber-critical-400/10';
      case 'created':
        return 'text-cyber-warning-400 bg-cyber-warning-400/10';
      case 'paused':
        return 'text-yellow-400 bg-yellow-400/10';
      default:
        return 'text-text-muted bg-background-secondary';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'text-cyber-critical-400 bg-cyber-critical-400/10';
      case 'high':
        return 'text-red-400 bg-red-400/10';
      case 'medium':
        return 'text-cyber-warning-400 bg-cyber-warning-400/10';
      case 'low':
        return 'text-green-400 bg-green-400/10';
      default:
        return 'text-text-muted bg-background-secondary';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const parseTTPs = (ttpsString: string) => {
    try {
      if (!ttpsString) return [];
      // Handle both JSON array and comma-separated strings
      if (ttpsString.startsWith('[')) {
        return JSON.parse(ttpsString);
      }
      return ttpsString.split(',').map(ttp => ttp.trim()).filter(Boolean);
    } catch {
      return ttpsString ? [ttpsString] : [];
    }
  };

  const parseIOCs = (iocsString: string) => {
    try {
      if (!iocsString) return [];
      if (iocsString.startsWith('[') || iocsString.startsWith('{')) {
        return JSON.parse(iocsString);
      }
      return iocsString.split(',').map(ioc => ioc.trim()).filter(Boolean);
    } catch {
      return iocsString ? [iocsString] : [];
    }
  };

  const currentTTPs = parseTTPs(hunt.curr_ttps || '');
  const predictedTTPs = parseTTPs(hunt.pred_ttps || '');
  const iocs = parseIOCs(hunt.iocs || '');

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h4 className="text-lg font-semibold text-text-primary mb-2">
              {hunt.title}
            </h4>
            <p className="text-sm text-text-muted mb-3">
              {hunt.hypothesis}
            </p>
            <div className="flex flex-wrap gap-2">
              <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(hunt.status)}`}>
                {hunt.status.toUpperCase()}
              </span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(hunt.priority)}`}>
                {hunt.priority.toUpperCase()}
              </span>
              <span className="px-2 py-1 rounded text-xs font-medium text-primary-400 bg-primary-400/10">
                {hunt.hunt_type.toUpperCase()}
              </span>
              {hunt.source_type && (
                <span className="px-2 py-1 rounded text-xs font-medium text-text-secondary bg-background-secondary">
                  {hunt.source_type}
                </span>
              )}
            </div>
          </div>
          <button
            onClick={handleExpand}
            className="ml-4 px-3 py-1 text-xs bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors"
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </button>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent>
          <div className="space-y-4 border-t border-border-primary pt-4">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 className="text-sm font-semibold text-text-primary mb-2">Hunt Details</h5>
                <div className="space-y-1 text-xs">
                  <div><span className="text-text-muted">ID:</span> <span className="font-mono">{hunt.hunt_id}</span></div>
                  <div><span className="text-text-muted">Status:</span> <span>{hunt.hunt_status}</span></div>
                  <div><span className="text-text-muted">Created:</span> <span>{formatDate(hunt.created_at)}</span></div>
                  {hunt.updated_at && (
                    <div><span className="text-text-muted">Updated:</span> <span>{formatDate(hunt.updated_at)}</span></div>
                  )}
                </div>
              </div>
              
              <div>
                <h5 className="text-sm font-semibold text-text-primary mb-2">Actions</h5>
                <div className="text-xs text-text-muted">
                  {hunt.actions || 'No actions specified'}
                </div>
              </div>
            </div>

            {/* TTPs Section */}
            {(currentTTPs.length > 0 || predictedTTPs.length > 0) && (
              <div>
                <h5 className="text-sm font-semibold text-text-primary mb-2">MITRE ATT&CK TTPs</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentTTPs.length > 0 && (
                    <div>
                      <h6 className="text-xs font-medium text-cyber-matrix-400 mb-1">Current TTPs</h6>
                      <div className="flex flex-wrap gap-1">
                        {currentTTPs.slice(0, 5).map((ttp, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-cyber-matrix-400/10 text-cyber-matrix-400 text-xs rounded"
                          >
                            {ttp}
                          </span>
                        ))}
                        {currentTTPs.length > 5 && (
                          <span className="px-2 py-1 bg-background-secondary text-text-muted text-xs rounded">
                            +{currentTTPs.length - 5} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {predictedTTPs.length > 0 && (
                    <div>
                      <h6 className="text-xs font-medium text-cyber-warning-400 mb-1">Predicted TTPs</h6>
                      <div className="flex flex-wrap gap-1">
                        {predictedTTPs.slice(0, 5).map((ttp, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-cyber-warning-400/10 text-cyber-warning-400 text-xs rounded"
                          >
                            {ttp}
                          </span>
                        ))}
                        {predictedTTPs.length > 5 && (
                          <span className="px-2 py-1 bg-background-secondary text-text-muted text-xs rounded">
                            +{predictedTTPs.length - 5} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* IOCs Section */}
            {iocs.length > 0 && (
              <div>
                <h5 className="text-sm font-semibold text-text-primary mb-2">Indicators of Compromise</h5>
                <div className="flex flex-wrap gap-1">
                  {iocs.slice(0, 8).map((ioc, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-cyber-critical-400/10 text-cyber-critical-400 text-xs rounded font-mono"
                    >
                      {typeof ioc === 'string' ? ioc : JSON.stringify(ioc)}
                    </span>
                  ))}
                  {iocs.length > 8 && (
                    <span className="px-2 py-1 bg-background-secondary text-text-muted text-xs rounded">
                      +{iocs.length - 8} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Additional Fields */}
            {(hunt.data_sources || hunt.detection_rules || hunt.false_positives) && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                {hunt.data_sources && (
                  <div>
                    <h6 className="font-medium text-text-primary mb-1">Data Sources</h6>
                    <div className="text-text-muted">{hunt.data_sources}</div>
                  </div>
                )}
                {hunt.detection_rules && (
                  <div>
                    <h6 className="font-medium text-text-primary mb-1">Detection Rules</h6>
                    <div className="text-text-muted">{hunt.detection_rules}</div>
                  </div>
                )}
                {hunt.false_positives && (
                  <div>
                    <h6 className="font-medium text-text-primary mb-1">False Positives</h6>
                    <div className="text-text-muted">{hunt.false_positives}</div>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
