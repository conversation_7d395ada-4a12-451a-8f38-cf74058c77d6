import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@telesoft/ui';
import { ThreatHunt } from '@telesoft/types';

interface ThreatHuntCardProps {
  hunt: ThreatHunt;
  onExpand?: (hunt: ThreatHunt) => void;
}

export function ThreatHuntCard({ hunt, onExpand }: ThreatHuntCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'bg-cyber-matrix-500/20 text-cyber-matrix-400';
      case 'completed':
        return 'bg-green-500/20 text-green-400';
      case 'failed':
        return 'bg-cyber-critical-500/20 text-cyber-critical-400';
      case 'created':
        return 'bg-cyber-warning-500/20 text-cyber-warning-400';
      case 'paused':
        return 'bg-gray-500/20 text-gray-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const parseTTPs = (ttpsString: string) => {
    try {
      // Extract TTP codes from the string
      const ttpMatches = ttpsString.match(/T\d{4}(?:\.\d{3})?/g);
      return ttpMatches || [];
    } catch {
      return [];
    }
  };

  const parseIOCs = (iocsString: string) => {
    try {
      const parsed = JSON.parse(iocsString);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (onExpand && !isExpanded) {
      onExpand(hunt);
    }
  };

  return (
    <Card className="hover:bg-background-secondary transition-colors">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1 mr-4">
            <h4 className="font-semibold text-text-primary mb-1">
              {hunt.title}
            </h4>
            <p className="text-sm text-text-muted">
              Hunt ID: {hunt.hunt_id}
            </p>
          </div>
          <div className="flex flex-col items-end space-y-2">
            <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(hunt.hunt_status)}`}>
              {hunt.hunt_status}
            </span>
            <button
              onClick={toggleExpanded}
              className="text-xs text-primary-400 hover:text-primary-300 transition-colors"
            >
              {isExpanded ? 'Show Less' : 'Show More'}
            </button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-text-muted">Source:</span>
              <span className="ml-2 text-text-primary">{hunt.source_type}</span>
            </div>
            <div>
              <span className="text-text-muted">Time:</span>
              <span className="ml-2 text-text-primary">{formatDate(hunt.time)}</span>
            </div>
          </div>

          {/* Hypothesis */}
          <div>
            <h5 className="text-sm font-medium text-text-primary mb-1">Hypothesis</h5>
            <p className="text-sm text-text-muted line-clamp-3">
              {hunt.hypothesis}
            </p>
          </div>

          {/* Expanded Content */}
          {isExpanded && (
            <div className="space-y-4 border-t border-border-primary pt-4">
              {/* Current TTPs */}
              {hunt.curr_ttps && (
                <div>
                  <h5 className="text-sm font-medium text-text-primary mb-2">Current MITRE ATT&CK TTPs</h5>
                  <div className="flex flex-wrap gap-1">
                    {parseTTPs(hunt.curr_ttps).map((ttp, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-cyber-matrix-500/10 text-cyber-matrix-400 text-xs rounded"
                      >
                        {ttp}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Predicted TTPs */}
              {hunt.pred_ttps && (
                <div>
                  <h5 className="text-sm font-medium text-text-primary mb-2">Predicted TTPs</h5>
                  <div className="flex flex-wrap gap-1">
                    {parseTTPs(hunt.pred_ttps).map((ttp, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-cyber-warning-500/10 text-cyber-warning-400 text-xs rounded"
                      >
                        {ttp}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* IOCs */}
              {hunt.iocs && (
                <div>
                  <h5 className="text-sm font-medium text-text-primary mb-2">Indicators of Compromise</h5>
                  <div className="space-y-1">
                    {parseIOCs(hunt.iocs).map((ioc, index) => (
                      <div key={index} className="text-xs bg-background-tertiary p-2 rounded">
                        {typeof ioc === 'object' ? JSON.stringify(ioc, null, 2) : ioc}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              {hunt.actions && (
                <div>
                  <h5 className="text-sm font-medium text-text-primary mb-2">Actions</h5>
                  <div className="text-xs bg-background-tertiary p-2 rounded">
                    <pre className="whitespace-pre-wrap">{hunt.actions}</pre>
                  </div>
                </div>
              )}

              {/* Additional Details */}
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span className="text-text-muted">Key:</span>
                  <span className="ml-2 text-text-primary">{hunt.key}</span>
                </div>
                <div>
                  <span className="text-text-muted">Extra Research:</span>
                  <span className="ml-2 text-text-primary">{hunt.extra_research}</span>
                </div>
              </div>

              {/* Error Message */}
              {hunt.error_message && (
                <div className="bg-cyber-critical-500/10 border border-cyber-critical-500/20 rounded p-3">
                  <h5 className="text-sm font-medium text-cyber-critical-400 mb-1">Error</h5>
                  <p className="text-xs text-cyber-critical-300">{hunt.error_message}</p>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
