export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface ThreatIncident {
  incident_type: 'beaconing' | 'spike' | 'anomaly' | 'ddos' | 'outlier' | 'dga' ;
  risk_severity: 'info' | 'unknown' | 'low' | 'medium' | 'high' | 'critical';
  investigation_status:
    | 'created'
    | 'running'
    | 'complete'
    | 'failed'
    | 'unknown';
  investigation_outcome?: 'closed' | 'automation triggered' | 'escalated' | 'action taken';
  time: number;
  uid: string;
  summary?: string;
  incident_description?: string;
  remediation_actions?: string;
  risk_message?: string;
  current_ttps?: string[];
  future_ttps?: string[];
}

export interface ThreatsResponse {
  data: {
    incidents: ThreatIncident[];
  };
  source: 'api' | 'cache';
  timestamp: string;
}

export interface ThreatHunt {
  hunt_id: string;
  title: string;
  hypothesis: string;
  status: 'running' | 'completed' | 'failed' | 'created' | 'paused';
  priority: 'low' | 'medium' | 'high' | 'critical';
  hunt_type: 'automated' | 'manual' | 'scheduled';
  hunt_status: string;
  actions: string;
  curr_ttps: string;
  pred_ttps: string;
  iocs: string;
  key: string;
  query?: string;
  research_sources?: string;
  result_found?: string;
  source: string;
  source_type: string;
  text: string;
  time: number;
  error_message?: string;
  extra_research: number;
  filter_threats?: string;
  html_results?: string;
  created_at?: number;
  updated_at?: number;
  created_by?: string;
  assigned_to?: string;
}

export interface ThreatHuntsResponse {
  threat_hunts: ThreatHunt[];
}

export interface Deployment {
  data: { [timestamp: string]: number };
  name: string;
  namespace: string;
  display_name?: string;
}

export interface DeploymentsResponse {
  data: {
    deployments: Deployment[];
  };
  source: 'api' | 'cache' | 'websocket';
  timestamp: string;
}
