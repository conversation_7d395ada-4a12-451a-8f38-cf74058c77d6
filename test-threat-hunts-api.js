// Simple test script to verify the threat hunts API integration
const fetch = require('node-fetch');

async function testThreatHuntsAPI() {
  try {
    console.log('Testing threat hunts API endpoint...');
    
    // Test the backend API directly
    console.log('\n1. Testing backend API directly:');
    const backendResponse = await fetch('http://***********:5000/api/v1/threat_hunts');
    
    if (!backendResponse.ok) {
      throw new Error(`Backend API failed: ${backendResponse.status} ${backendResponse.statusText}`);
    }
    
    const backendData = await backendResponse.json();
    console.log(`✓ Backend API working - Found ${backendData.threat_hunts?.length || 0} threat hunts`);
    
    if (backendData.threat_hunts && backendData.threat_hunts.length > 0) {
      const firstHunt = backendData.threat_hunts[0];
      console.log(`  Sample hunt: ${firstHunt.title} (${firstHunt.hunt_status})`);
    }
    
    // Test the frontend API route (if running)
    console.log('\n2. Testing frontend API route:');
    try {
      const frontendResponse = await fetch('http://localhost:3000/api/v1/threat-hunts');
      
      if (frontendResponse.ok) {
        const frontendData = await frontendResponse.json();
        console.log(`✓ Frontend API route working - Found ${frontendData.threat_hunts?.length || 0} threat hunts`);
      } else {
        console.log(`⚠ Frontend API route not available (${frontendResponse.status})`);
        console.log('  This is expected if the frontend is not running');
      }
    } catch (error) {
      console.log('⚠ Frontend API route not available (connection refused)');
      console.log('  This is expected if the frontend is not running');
    }
    
    console.log('\n✓ API integration test completed successfully!');
    
  } catch (error) {
    console.error('\n✗ API integration test failed:', error.message);
    process.exit(1);
  }
}

testThreatHuntsAPI();
