/**
 * Next.js API Route: /api/v1/threat-hunts
 * Proxies requests to the backend threat hunts endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  backendApiProxy,
  BackendApiError,
} from '../../../../lib/backend-proxy';

export async function GET(request: NextRequest) {
  try {
    console.log('[API Route] GET /api/v1/threat-hunts - Proxying to backend...');

    // Forward the request to the backend with proper error handling
    let response;
    try {
      response = await backendApiProxy.forwardRequest('/api/v1/threat_hunts', {
        method: 'GET',
      });
    } catch (proxyError: unknown) {
      console.error('[API Route] Backend proxy error:', proxyError);

      if (proxyError instanceof BackendApiError) {
        return NextResponse.json(
          {
            error: 'Backend Service Error',
            message: proxyError.message,
            timestamp: new Date().toISOString(),
          },
          { status: proxyError.status || 500 },
        );
      }

      return NextResponse.json(
        {
          error: 'Internal Server Error',
          message: 'Failed to connect to backend service',
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      );
    }

    if (!response.ok) {
      console.error(
        `[API Route] Backend returned error: ${response.status} ${response.statusText}`,
      );

      let errorBody;
      try {
        errorBody = await response.text();
      } catch {
        errorBody = 'Unknown error';
      }

      return NextResponse.json(
        {
          error: 'Backend Error',
          message: `Backend service returned ${response.status}: ${response.statusText}`,
          details: errorBody,
          timestamp: new Date().toISOString(),
        },
        { status: response.status },
      );
    }

    // Parse and return the response
    let data;
    try {
      data = await response.json();
    } catch (parseError) {
      console.error('[API Route] Failed to parse backend response:', parseError);
      return NextResponse.json(
        {
          error: 'Parse Error',
          message: 'Failed to parse backend response',
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      );
    }

    console.log('[API Route] Successfully proxied threat hunts request');

    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error('[API Route] Unexpected error:', error);

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'An unexpected error occurred',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Health check endpoint
 */
export async function HEAD(_request: NextRequest) {
  try {
    // Perform a lightweight check to the backend
    const response = await backendApiProxy.forwardRequest('/api/v1/threat_hunts', {
      method: 'HEAD',
    });

    return new NextResponse(null, {
      status: response.status,
      headers: {
        'X-Health-Check': 'ok',
        'X-Backend-Status': response.status.toString(),
      },
    });
  } catch (error) {
    console.error('[API Route] Health check failed:', error);

    return new NextResponse(null, {
      status: 503,
      headers: {
        'X-Health-Check': 'failed',
        'X-Backend-Status': 'unavailable',
      },
    });
  }
}
