'use client';

import { <PERSON>, CardHeader, CardContent } from '@telesoft/ui';
import { ThreatFlowDiagram } from '@telesoft/d3';
import { useState, useMemo, useEffect } from 'react';
import { classNames, STYLE_PRESETS } from '@telesoft/utils';
import { useThreatHunts, useThreatHuntStats } from '../../lib/hooks/useThreatHunts';
import { ThreatHuntCard } from '../../components/ThreatHuntCard';

export default function ThreatHunting() {
  const [sourceAActive, setSourceAActive] = useState(false);
  const [sourceBActive, setSourceBActive] = useState(false);
  const [processedActive, setProcessedActive] = useState(false);

  // Fetch real threat hunts data
  const { threatHunts, loading, error, refresh } = useThreatHunts();
  const stats = useThreatHuntStats(threatHunts);

  // Additional dynamic state for status cards
  const [detectionRate, setDetectionRate] = useState(94.7);
  const [systemHealth, setSystemHealth] = useState({
    processingLoad: 67,
    memoryUsage: 42,
  });

  // Continuous animation cycle - keep everything active with minor metric updates
  useEffect(() => {
    // Set everything to active immediately for constant "analyzing" state
    setSourceAActive(true);
    setSourceBActive(true);
    setProcessedActive(true);

    const updateMetrics = () => {

      setDetectionRate((prev) =>
        Math.max(92, Math.min(98, prev + (Math.random() - 0.5) * 0.2)),
      );

      setSystemHealth((prev) => ({
        processingLoad: Math.max(
          45,
          Math.min(85, prev.processingLoad + (Math.random() - 0.5) * 3),
        ),
        memoryUsage: Math.max(
          35,
          Math.min(70, prev.memoryUsage + (Math.random() - 0.5) * 2),
        ),
      }));
    };

    // Initial update
    updateMetrics();

    // Set up continuous metric updates
    const metricsInterval = setInterval(
      () => {
        updateMetrics();
      },
      2000 + Math.random() * 2000,
    ); // Update every 2-4 seconds

    return () => {
      clearInterval(metricsInterval);
    };
  }, []);

  // D3 Flow Diagram Data - simplified to 3 source boxes
  const flowNodes = useMemo(
    () => [
      // Simplified Source Groups
      {
        id: 'threat-intelligence',
        label: 'Threat Intelligence',
        description: 'CTX, MISP & IOC Feeds',
        type: 'source' as const,
        active: sourceAActive,
      },
      {
        id: 'news',
        label: 'News',
        description: 'Threat Summaries & Intelligence',
        type: 'source' as const,
        active: sourceBActive,
      },
      {
        id: 'playbook',
        label: 'Playbook',
        description: 'Security Policies & Rules',
        type: 'source' as const,
        active: sourceBActive,
      },
      // Central Processor
      {
        id: 'processor',
        label: 'Correlation Engine',
        description: 'Advanced Analytics',
        type: 'processor' as const,
        metrics: [
          {
            label: 'Correlation Rate',
            value: processedActive
              ? `${(97.5 + Math.random() * 2).toFixed(1)}%`
              : '0%',
            status: 'good' as const,
          },
          {
            label: 'Active Rules',
            value: processedActive
              ? Math.floor(150 + Math.random() * 15).toString()
              : '0',
            status: 'good' as const,
          },
        ],
        active: processedActive,
      },
      // Outputs
      {
        id: 'output-conclusive',
        label: 'Conclusive',
        description: processedActive
          ? Math.floor(8 + Math.random() * 8).toString()
          : '0',
        type: 'output' as const,
        metrics: [],
        active: processedActive,
      },
      {
        id: 'output-inconclusive',
        label: 'Inconclusive',
        description: processedActive
          ? Math.floor(520 + Math.random() * 80).toString()
          : '0',
        type: 'output' as const,
        metrics: [],
        active: processedActive,
      },
    ],
    [sourceAActive, sourceBActive, processedActive],
  );

  const flowLinks = useMemo(
    () => [
      // Simplified links from 3 sources to processor
      {
        source: 'threat-intelligence',
        target: 'processor',
        active: sourceAActive,
      },
      { source: 'news', target: 'processor', active: sourceBActive },
      { source: 'playbook', target: 'processor', active: sourceBActive },
      // Processor to outputs
      {
        source: 'processor',
        target: 'output-conclusive',
        active: processedActive,
      },
      {
        source: 'processor',
        target: 'output-inconclusive',
        active: processedActive,
      },
    ],
    [sourceAActive, sourceBActive, processedActive],
  );

  return (
    <div className={classNames(STYLE_PRESETS.pageContainer)}>
      <div className={classNames(STYLE_PRESETS.contentContainer)}>


        {/* D3 Interactive Flow Diagram */}
        <div className="mt-0">
          <CardContent>
            <div className="bg-background-primary rounded-lg p-6 border border-border-primary flex items-center justify-center min-h-[450px]">
              <ThreatFlowDiagram
                nodes={flowNodes}
                links={flowLinks}
                width={800}
                height={400}
                onNodeClick={(node) => {
                  console.log('Node clicked:', node);
                  // Handle node interactions
                }}
                className="mx-auto"
              />
            </div>
          </CardContent>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Active Hunts
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Running</span>
                  <span className="text-sm font-mono text-cyber-matrix-400">
                    {loading ? '...' : stats.running}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Created</span>
                  <span className="text-sm font-mono text-cyber-warning-400">
                    {loading ? '...' : stats.created}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Completed</span>
                  <span className="text-sm font-mono text-text-secondary">
                    {loading ? '...' : stats.completed}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Total</span>
                  <span className="text-sm font-mono text-text-primary">
                    {loading ? '...' : stats.total}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Detection Rate
              </h3>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-mono text-primary-400 mb-2">
                  {detectionRate.toFixed(1)}%
                </div>
                <div className="text-sm text-text-muted">Last 24 hours</div>
                <div className="mt-4 h-2 bg-background-tertiary rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-primary-500 to-cyber-matrix-500 rounded-full transition-all duration-1000"
                    style={{ width: `${detectionRate}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                System Health
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Sources Online
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-cyber-matrix-400 rounded-full animate-pulse" />
                    <span className="text-sm font-mono text-cyber-matrix-400">
                      2/2
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Processing Load
                  </span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.processingLoad)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">Memory Usage</span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.memoryUsage)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-6">
            <Card>
              <CardContent>
                <div className="text-center text-cyber-critical-400">
                  <p className="text-lg font-semibold mb-2">Error Loading Threat Hunts</p>
                  <p className="text-sm text-text-muted">{error}</p>
                  <button
                    onClick={refresh}
                    className="mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Recent Threat Hunts */}
        {!loading && !error && threatHunts.length > 0 && (
          <div className="mt-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-text-primary">
                Recent Threat Hunts ({threatHunts.length})
              </h3>
              <p className="text-sm text-text-muted">
                Latest threat hunting activities and their current status
              </p>
            </div>
            <div className="space-y-4">
              {stats.recentHunts.slice(0, 10).map((hunt) => (
                <ThreatHuntCard
                  key={hunt.hunt_id}
                  hunt={hunt}
                  onExpand={(hunt) => {
                    console.log('Expanded threat hunt:', hunt);
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* No Data State */}
        {!loading && !error && threatHunts.length === 0 && (
          <div className="mt-6">
            <Card>
              <CardContent>
                <div className="text-center text-text-muted py-8">
                  <p className="text-lg font-semibold mb-2">No Threat Hunts Found</p>
                  <p className="text-sm">No threat hunting data is currently available.</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
