'use client';

import { <PERSON>, CardHeader, CardContent } from '@telesoft/ui';
import { ThreatFlowDiagram } from '@telesoft/d3';
import { useState, useMemo, useEffect } from 'react';
import { classNames, STYLE_PRESETS } from '@telesoft/utils';
import { useThreatHunts, useThreatHuntStats } from '../../lib/hooks/useThreatHunts';
import { ThreatHuntCard } from '../../components/ThreatHuntCard';

export default function ThreatHunting() {
  const [sourceAActive, setSourceAActive] = useState(false);
  const [sourceBActive, setSourceBActive] = useState(false);
  const [processedActive, setProcessedActive] = useState(false);

  // Fetch real threat hunts data
  const { threatHunts, loading, error, refresh } = useThreatHunts();
  const stats = useThreatHuntStats(threatHunts);

  // Selected source type filter
  const [selectedSourceType, setSelectedSourceType] = useState<string | null>(null);
  const [detectionRate, setDetectionRate] = useState(94.7);
  const [systemHealth, setSystemHealth] = useState({
    processingLoad: 67,
    memoryUsage: 42,
  });

  // Calculate source type statistics
  const sourceTypeStats = useMemo(() => {
    const stats: Record<string, number> = {};
    threatHunts.forEach(hunt => {
      const sourceType = hunt.source_type || 'unknown';
      stats[sourceType] = (stats[sourceType] || 0) + 1;
    });
    return Object.keys(stats)
      .map(type => ({ type, count: stats[type] }))
      .sort((a, b) => b.count - a.count);
  }, [threatHunts]);

  // Function to get display label for source type
  const getSourceTypeLabel = (sourceType: string): string => {
    const labelMap: Record<string, string> = {
      'threat_intel': 'Threat Intelligence',
      'misp': 'Threat Intelligence: MISP',
      'cisa': 'CISA KEV',
      'news': 'News',
      'playbook': 'Playbook',
      'manual': 'Manual Hunt',
      'automated': 'Automated Hunt',
      'unknown': 'Unknown Source'
    };
    return labelMap[sourceType] || sourceType.charAt(0).toUpperCase() + sourceType.slice(1);
  };

  // Filter threat hunts by selected source type
  const filteredThreatHunts = useMemo(() => {
    if (!selectedSourceType) return threatHunts;
    return threatHunts.filter(hunt => hunt.source_type === selectedSourceType);
  }, [threatHunts, selectedSourceType]);

  // Continuous animation cycle - keep everything active with minor metric updates
  useEffect(() => {
    // Set everything to active immediately for constant "analyzing" state
    setSourceAActive(true);
    setSourceBActive(true);
    setProcessedActive(true);

    const updateMetrics = () => {

      setDetectionRate((prev) =>
        Math.max(92, Math.min(98, prev + (Math.random() - 0.5) * 0.2)),
      );

      setSystemHealth((prev) => ({
        processingLoad: Math.max(
          45,
          Math.min(85, prev.processingLoad + (Math.random() - 0.5) * 3),
        ),
        memoryUsage: Math.max(
          35,
          Math.min(70, prev.memoryUsage + (Math.random() - 0.5) * 2),
        ),
      }));
    };

    // Initial update
    updateMetrics();

    // Set up continuous metric updates
    const metricsInterval = setInterval(
      () => {
        updateMetrics();
      },
      2000 + Math.random() * 2000,
    ); // Update every 2-4 seconds

    return () => {
      clearInterval(metricsInterval);
    };
  }, []);

  // D3 Flow Diagram Data - dynamic based on real source types
  const flowNodes = useMemo(
    () => {
      // Create source nodes from real data
      const sourceNodes = sourceTypeStats.slice(0, 3).map((source, index) => ({
        id: source.type,
        label: getSourceTypeLabel(source.type),
        description: `${source.count} Threat Hunts`,
        type: 'source' as const,
        active: true,
        count: source.count,
      }));

      // Add default sources if we don't have enough data
      while (sourceNodes.length < 3) {
        const defaultSources = [
          { type: 'threat_intel', label: 'Threat Intelligence', description: 'CTX, MISP & IOC Feeds' },
          { type: 'news', label: 'News', description: 'Threat Summaries & Intelligence' },
          { type: 'playbook', label: 'Playbook', description: 'Security Policies & Rules' }
        ];
        const defaultSource = defaultSources[sourceNodes.length];
        sourceNodes.push({
          id: defaultSource.type,
          label: defaultSource.label,
          description: defaultSource.description,
          type: 'source' as const,
          active: false,
          count: 0,
        });
      }

      return [
        ...sourceNodes,
        // Central Processor
        {
          id: 'processor',
          label: 'Correlation Engine',
          description: 'Advanced Analytics',
          type: 'processor' as const,
          metrics: [
            {
              label: 'Correlation Rate',
              value: processedActive
                ? `${(97.5 + Math.random() * 2).toFixed(1)}%`
                : '0%',
              status: 'good' as const,
            },
            {
              label: 'Active Rules',
              value: processedActive
                ? Math.floor(150 + Math.random() * 15).toString()
                : '0',
              status: 'good' as const,
            },
          ],
          active: processedActive,
        },
        // Outputs
        {
          id: 'output-conclusive',
          label: 'Conclusive',
          description: processedActive
            ? Math.floor(8 + Math.random() * 8).toString()
            : '0',
          type: 'output' as const,
          metrics: [],
          active: processedActive,
        },
        {
          id: 'output-inconclusive',
          label: 'Inconclusive',
          description: processedActive
            ? Math.floor(520 + Math.random() * 80).toString()
            : '0',
          type: 'output' as const,
          metrics: [],
          active: processedActive,
        },
      ];
    },
    [sourceTypeStats, sourceAActive, sourceBActive, processedActive],
  );

  const flowLinks = useMemo(
    () => [
      // Simplified links from 3 sources to processor
      {
        source: 'threat-intelligence',
        target: 'processor',
        active: sourceAActive,
      },
      { source: 'news', target: 'processor', active: sourceBActive },
      { source: 'playbook', target: 'processor', active: sourceBActive },
      // Processor to outputs
      {
        source: 'processor',
        target: 'output-conclusive',
        active: processedActive,
      },
      {
        source: 'processor',
        target: 'output-inconclusive',
        active: processedActive,
      },
    ],
    [sourceAActive, sourceBActive, processedActive],
  );

  return (
    <div className={classNames(STYLE_PRESETS.pageContainer)}>
      <div className={classNames(STYLE_PRESETS.contentContainer)}>

        {/* Dynamic Sources Section */}
        <div className="mb-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Threat Hunt Sources
              </h3>
              <p className="text-sm text-text-muted">
                Active sources feeding threat hunting activities
              </p>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-4">
                  <div className="text-sm text-text-muted">Loading sources...</div>
                </div>
              ) : error ? (
                <div className="text-center py-4">
                  <div className="text-sm text-cyber-critical-400">Error loading sources</div>
                  <button
                    onClick={refresh}
                    className="mt-2 px-3 py-1 bg-primary-500 text-white text-xs rounded hover:bg-primary-600 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              ) : sourceTypeStats.length === 0 ? (
                <div className="text-center py-4">
                  <div className="text-sm text-text-muted">No sources available</div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {sourceTypeStats.map((source) => (
                    <div
                      key={source.type}
                      className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${selectedSourceType === source.type
                        ? 'border-primary-400 bg-primary-500/10'
                        : 'border-border-primary hover:border-primary-300 hover:bg-background-secondary'
                        }`}
                      onClick={() => setSelectedSourceType(
                        selectedSourceType === source.type ? null : source.type
                      )}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-semibold text-text-primary text-sm">
                          {getSourceTypeLabel(source.type)}
                        </h4>
                        <span className="text-xs bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
                          {source.count}
                        </span>
                      </div>
                      <div className="text-xs text-text-muted">
                        {source.count === 1 ? '1 threat hunt' : `${source.count} threat hunts`}
                      </div>
                      {selectedSourceType === source.type && (
                        <div className="mt-2 text-xs text-primary-400">
                          ✓ Filtering active
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {selectedSourceType && (
                <div className="mt-4 p-3 bg-primary-500/10 border border-primary-500/20 rounded">
                  <div className="text-sm text-primary-400">
                    Showing {filteredThreatHunts.length} threat hunts from "{getSourceTypeLabel(selectedSourceType)}"
                    <button
                      onClick={() => setSelectedSourceType(null)}
                      className="ml-2 text-xs underline hover:no-underline"
                    >
                      Clear filter
                    </button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* D3 Interactive Flow Diagram */}
        <div className="mt-0">
          <CardContent>
            <div className="bg-background-primary rounded-lg p-6 border border-border-primary flex items-center justify-center min-h-[450px]">
              <ThreatFlowDiagram
                nodes={flowNodes}
                links={flowLinks}
                width={800}
                height={400}
                onNodeClick={(node) => {
                  console.log('Node clicked:', node);
                  // Handle node interactions
                }}
                className="mx-auto"
              />
            </div>
          </CardContent>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Active Hunts
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Running</span>
                  <span className="text-sm font-mono text-cyber-matrix-400">
                    {loading ? '...' : stats.running}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Created</span>
                  <span className="text-sm font-mono text-cyber-warning-400">
                    {loading ? '...' : stats.created}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Completed</span>
                  <span className="text-sm font-mono text-text-secondary">
                    {loading ? '...' : stats.completed}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Total</span>
                  <span className="text-sm font-mono text-text-primary">
                    {loading ? '...' : stats.total}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Detection Rate
              </h3>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-mono text-primary-400 mb-2">
                  {detectionRate.toFixed(1)}%
                </div>
                <div className="text-sm text-text-muted">Last 24 hours</div>
                <div className="mt-4 h-2 bg-background-tertiary rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-primary-500 to-cyber-matrix-500 rounded-full transition-all duration-1000"
                    style={{ width: `${detectionRate}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                System Health
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Sources Online
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-cyber-matrix-400 rounded-full animate-pulse" />
                    <span className="text-sm font-mono text-cyber-matrix-400">
                      2/2
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Processing Load
                  </span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.processingLoad)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">Memory Usage</span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.memoryUsage)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Threat Hunt List */}
        {!loading && !error && (filteredThreatHunts.length > 0 || threatHunts.length > 0) && (
          <div className="mt-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-text-primary">
                {selectedSourceType
                  ? `Threat Hunts from ${getSourceTypeLabel(selectedSourceType)} (${filteredThreatHunts.length})`
                  : `Recent Threat Hunts (${threatHunts.length})`
                }
              </h3>
              <p className="text-sm text-text-muted">
                {selectedSourceType
                  ? `Filtered results from ${getSourceTypeLabel(selectedSourceType)} source`
                  : 'Latest threat hunting activities and their current status'
                }
              </p>
            </div>
            <div className="space-y-4">
              {(selectedSourceType ? filteredThreatHunts : threatHunts)
                .slice(0, 10)
                .map((hunt) => (
                  <ThreatHuntCard
                    key={hunt.hunt_id}
                    hunt={hunt}
                    onExpand={(hunt) => {
                      console.log('Expanded threat hunt:', hunt);
                    }}
                  />
                ))}
            </div>

            {selectedSourceType && filteredThreatHunts.length === 0 && (
              <div className="text-center py-8">
                <div className="text-text-muted">
                  <p className="text-lg font-semibold mb-2">No threat hunts found</p>
                  <p className="text-sm">No threat hunts available for the selected source type.</p>
                  <button
                    onClick={() => setSelectedSourceType(null)}
                    className="mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors"
                  >
                    Show All Threat Hunts
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="mt-6">
            <Card>
              <CardContent>
                <div className="text-center text-cyber-critical-400">
                  <p className="text-lg font-semibold mb-2">Error Loading Threat Hunts</p>
                  <p className="text-sm text-text-muted">{error}</p>
                  <button
                    onClick={refresh}
                    className="mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* No Data State */}
        {!loading && !error && threatHunts.length === 0 && (
          <div className="mt-6">
            <Card>
              <CardContent>
                <div className="text-center text-text-muted py-8">
                  <p className="text-lg font-semibold mb-2">No Threat Hunts Found</p>
                  <p className="text-sm">No threat hunting data is currently available.</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
